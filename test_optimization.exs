#!/usr/bin/env elixir

# Simple script to test the SQLite optimization
# This script will introspect a table and show the queries being executed

Mix.install([
  {:ecto_sql, "~> 3.13"},
  {:sqlite3, "~> 0.14"}
])

defmodule TestRepo do
  use Ecto.Repo,
    otp_app: :test_app,
    adapter: Ecto.Adapters.SQLite3
end

# Configure the repo
Application.put_env(:test_app, TestRepo,
  database: ":memory:",
  log: true
)

# Start the repo
{:ok, _} = TestRepo.start_link()

# Create a test table with multiple columns
TestRepo.query!("""
CREATE TABLE users (
  id INTEGER PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  age INTEGER CHECK (age >= 0),
  active BOOLEAN DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
""")

# Add the drops_relation code path
Code.append_path("_build/dev/lib/drops_relation/ebin")

# Now test the introspection
IO.puts("Testing table introspection with optimization...")
IO.puts("Watch for duplicate 'SELECT sql FROM sqlite_master' queries:")
IO.puts("")

{:ok, table} = Drops.SQL.Database.table("users", TestRepo)

IO.puts("")
IO.puts("Table introspection completed successfully!")
IO.puts("Table: #{table.name}")
IO.puts("Columns: #{length(table.columns)}")
IO.puts("Adapter: #{table.adapter}")

# Verify check constraints were extracted
for column <- table.columns do
  if length(column.meta.check_constraints) > 0 do
    IO.puts("Column #{column.name} has check constraints: #{inspect(column.meta.check_constraints)}")
  end
end
